
import type { Metada<PERSON> } from 'next';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';
import LayoutClient from '@/components/layout/layout-client';

export const metadata: Metadata = {
  title: 'Auspex Records',
  description: 'Independent label committed to curating and cultivating future-facing sound.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className="antialiased">
        <LayoutClient>{children}</LayoutClient>
        <Toaster />
      </body>
    </html>
  );
}
