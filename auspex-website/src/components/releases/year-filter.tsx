"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface YearFilterProps {
  years: number[];
  selectedYear: number | 'all';
  onSelectYear: (year: number | 'all') => void;
}

export default function YearFilter({ years, selectedYear, onSelectYear }: YearFilterProps) {
  return (
    <div className="flex flex-wrap justify-center gap-2 rounded-full p-2 bg-black/30 backdrop-blur-lg border border-white/10 shadow-lg">
      <button
        onClick={() => onSelectYear('all')}
        className={cn(
          "relative text-lg font-medium transition-colors text-white/80 hover:text-white px-4 py-2 rounded-full",
        )}
      >
        Latest
        {selectedYear === 'all' && (
          <motion.div
            className="absolute inset-0 bg-primary/20 rounded-full -z-10"
            layoutId="active-year"
            transition={{
              type: "spring",
              stiffness: 500,
              damping: 30,
              mass: 0.8
            }}
          />
        )}
      </button>
      {years.map(year => (
        <button
          key={year}
          onClick={() => onSelectYear(year)}
          className={cn(
            "relative text-lg font-medium transition-colors text-white/80 hover:text-white px-4 py-2 rounded-full",
          )}
        >
          {year}
          {selectedYear === year && (
            <motion.div
              className="absolute inset-0 bg-primary/20 rounded-full -z-10"
              layoutId="active-year"
              transition={{
                type: "spring",
                stiffness: 500,
                damping: 30,
                mass: 0.8
              }}
            />
          )}
        </button>
      ))}
    </div>
  );
}
