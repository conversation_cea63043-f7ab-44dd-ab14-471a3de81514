
"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import type { Release } from '@/lib/types';
import YearFilter from '@/components/releases/year-filter';
import ReleaseCard from '@/components/releases/release-card';
import { AnimatePresence, motion } from 'framer-motion';
import { Skeleton } from '@/components/ui/skeleton';
import { getReleases } from '@/lib/data';
import ExpandedReleaseCard from './expanded-release-card';
import YouTubeEmbed from '../common/youtube-embed';
import { Button } from '../ui/button';
import { X } from 'lucide-react';

export default function ReleasesPageClient({ initialReleases, years }: { initialReleases: Release[], years: number[] }) {
  const [releases, setReleases] = useState<Release[]>(initialReleases);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedYear, setSelectedYear] = useState<number | 'all'>('all');
  const [selectedRelease, setSelectedRelease] = useState<Release | null>(null);
  const [playingVideoId, setPlayingVideoId] = useState<string | null>(null);
  
  const isPlayerVisible = !!playingVideoId;

  const fetchReleases = useCallback(async (year: number | 'all') => {
    console.log('fetchReleases called with year:', year);
    setIsLoading(true);
    try {
      const releases = await getReleases(year);
      console.log('Fetched releases:', releases.length, 'for year:', year);
      setReleases(releases);
    } catch (error) {
      console.error('Error fetching releases:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchReleases(selectedYear);
  }, [selectedYear, fetchReleases]);
  
  const handleReleaseClick = (release: Release) => {
    setSelectedRelease(release);
  };
  
  const handleCloseExpandedCard = useCallback(() => {
    setSelectedRelease(null);
  }, []);

  const handleTrackSelect = useCallback((videoId: string | null) => {
    setPlayingVideoId(videoId);
  }, []);
  
  const handleClosePlayer = useCallback(() => {
      setPlayingVideoId(null);
  }, []);

  const playerUrl = playingVideoId ? `https://www.youtube.com/embed/${playingVideoId}` : '';

  const renderSkeletons = () => (
     <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-8">
        {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="flex flex-col gap-2">
              <Skeleton className="w-full aspect-square rounded-lg" />
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-5 w-1/2" />
            </div>
        ))}
      </div>
  )

  return (
    <>
      <div className="container mx-auto px-4 py-8 pt-28">
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-4xl lg:text-5xl font-headline mb-4 text-center text-primary">Our Discography</h1>
          <p className="text-center text-lg text-foreground/70 max-w-3xl mx-auto mb-10">
            Dive into the sonic landscapes crafted by our artists. Explore our full catalog or filter by year to journey through our musical evolution.
          </p>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-12"
        >
          <YearFilter
            years={years}
            selectedYear={selectedYear}
            onSelectYear={(year) => {
              console.log('Year selected:', year);
              setSelectedYear(year);
            }}
          />
        </motion.div>

        <div className="flex-1">
          {isLoading ? (
            renderSkeletons()
          ) : releases.length > 0 ? (
             <motion.div 
              className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-8"
              initial="offscreen"
              whileInView="onscreen"
              viewport={{ once: true, amount: 0.1 }}
              transition={{ staggerChildren: 0.1 }}
            >
              {releases.map(release => (
                <ReleaseCard key={release.id} release={release} onClick={() => handleReleaseClick(release)} isSelected={selectedRelease?.id === release.id}/>
              ))}
            </motion.div>
          ) : (
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="flex items-center justify-center h-64 bg-card/50 rounded-lg"
            >
              <p className="text-muted-foreground">No releases found for the selected year.</p>
            </motion.div>
          )}
        </div>
      </div>

      <AnimatePresence>
        {selectedRelease && (
          <ExpandedReleaseCard 
            release={selectedRelease} 
            onClose={handleCloseExpandedCard} 
            onTrackSelect={handleTrackSelect}
            playingVideoId={playingVideoId}
          />
        )}
      </AnimatePresence>

      <div
        className="fixed bottom-4 right-4 z-[60] w-full max-w-sm"
        style={{ pointerEvents: isPlayerVisible ? 'auto' : 'none' }}
      >
        <AnimatePresence>
          {isPlayerVisible && (
            <motion.div
              initial={{ opacity: 0, y: 100 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 100 }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            >
              <div className="bg-card/80 backdrop-blur-lg rounded-lg shadow-2xl p-2">
                <div className="flex justify-end mb-1">
                  <Button variant="ghost" size="icon" className="h-6 w-6" onClick={handleClosePlayer}>
                    <X size={16}/>
                  </Button>
                </div>
                <div className="aspect-video">
                  <YouTubeEmbed url={playerUrl} autoplay={true} />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  );
}
