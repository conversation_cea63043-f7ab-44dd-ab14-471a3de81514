"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/releases/page",{

/***/ "(app-pages-browser)/./src/components/releases/year-filter.tsx":
/*!*************************************************!*\
  !*** ./src/components/releases/year-filter.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ YearFilter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction YearFilter(param) {\n    let { years, selectedYear, onSelectYear } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-wrap justify-center gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onSelectYear(\"all\"),\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"relative text-lg font-medium transition-colors text-white/80 hover:text-white px-4 py-2 rounded-full bg-black/30 backdrop-blur-lg border border-white/10 shadow-lg\"),\n                children: [\n                    \"Latest\",\n                    selectedYear === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute inset-0 bg-primary/20 rounded-full -z-10\",\n                        layoutId: \"active-year\",\n                        transition: {\n                            type: \"spring\",\n                            stiffness: 500,\n                            damping: 30,\n                            mass: 0.8\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/year-filter.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/year-filter.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            years.map((year)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onSelectYear(year),\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"relative text-lg font-medium transition-colors text-white/80 hover:text-white px-4 py-2 rounded-full\"),\n                    children: [\n                        year,\n                        selectedYear === year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute inset-0 bg-primary/20 rounded-full -z-10\",\n                            layoutId: \"active-year\",\n                            transition: {\n                                type: \"spring\",\n                                stiffness: 500,\n                                damping: 30,\n                                mass: 0.8\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/year-filter.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, year, true, {\n                    fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/year-filter.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/year-filter.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_c = YearFilter;\nvar _c;\n$RefreshReg$(_c, \"YearFilter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/releases/year-filter.tsx\n"));

/***/ })

});