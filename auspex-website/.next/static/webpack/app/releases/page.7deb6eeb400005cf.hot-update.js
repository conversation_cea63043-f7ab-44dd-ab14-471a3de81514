"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/releases/page",{

/***/ "(app-pages-browser)/./src/components/releases/releases-page-client.tsx":
/*!**********************************************************!*\
  !*** ./src/components/releases/releases-page-client.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ReleasesPageClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_releases_year_filter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/releases/year-filter */ \"(app-pages-browser)/./src/components/releases/year-filter.tsx\");\n/* harmony import */ var _components_releases_release_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/releases/release-card */ \"(app-pages-browser)/./src/components/releases/release-card.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/data */ \"(app-pages-browser)/./src/lib/data.ts\");\n/* harmony import */ var _expanded_release_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./expanded-release-card */ \"(app-pages-browser)/./src/components/releases/expanded-release-card.tsx\");\n/* harmony import */ var _common_youtube_embed__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../common/youtube-embed */ \"(app-pages-browser)/./src/components/common/youtube-embed.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ReleasesPageClient(param) {\n    let { initialReleases, years } = param;\n    _s();\n    const [releases, setReleases] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialReleases);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedRelease, setSelectedRelease] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [playingVideoId, setPlayingVideoId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const isPlayerVisible = !!playingVideoId;\n    const fetchReleases = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (year)=>{\n        setIsLoading(true);\n        try {\n            const releases = await (0,_lib_data__WEBPACK_IMPORTED_MODULE_5__.getReleases)(year);\n            setReleases(releases);\n        } catch (error) {\n            console.error(\"Error fetching releases:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchReleases(selectedYear);\n    }, [\n        selectedYear,\n        fetchReleases\n    ]);\n    const handleReleaseClick = (release)=>{\n        setSelectedRelease(release);\n    };\n    const handleCloseExpandedCard = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setSelectedRelease(null);\n    }, []);\n    const handleTrackSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((videoId)=>{\n        setPlayingVideoId(videoId);\n    }, []);\n    const handleClosePlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setPlayingVideoId(null);\n    }, []);\n    const playerUrl = playingVideoId ? \"https://www.youtube.com/embed/\".concat(playingVideoId) : \"\";\n    const renderSkeletons = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-8\",\n            children: Array.from({\n                length: 8\n            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                            className: \"w-full aspect-square rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                            className: \"h-6 w-3/4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                            className: \"h-5 w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, i, true, {\n                    fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 13\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n            lineNumber: 60,\n            columnNumber: 6\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8 pt-28\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl lg:text-5xl font-headline mb-4 text-center text-primary\",\n                                children: \"Our Discography\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-lg text-foreground/70 max-w-3xl mx-auto mb-10\",\n                                children: \"Dive into the sonic landscapes crafted by our artists. Explore our full catalog or filter by year to journey through our musical evolution.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.2\n                        },\n                        className: \"mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_releases_year_filter__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            years: years,\n                            selectedYear: selectedYear,\n                            onSelectYear: setSelectedYear\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: isLoading ? renderSkeletons() : releases.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-8\",\n                            initial: \"offscreen\",\n                            whileInView: \"onscreen\",\n                            viewport: {\n                                once: true,\n                                amount: 0.1\n                            },\n                            transition: {\n                                staggerChildren: 0.1\n                            },\n                            children: releases.map((release)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_releases_release_card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    release: release,\n                                    onClick: ()=>handleReleaseClick(release),\n                                    isSelected: (selectedRelease === null || selectedRelease === void 0 ? void 0 : selectedRelease.id) === release.id\n                                }, release.id, false, {\n                                    fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 14\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            className: \"flex items-center justify-center h-64 bg-card/50 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"No releases found for the selected year.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                children: selectedRelease && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_expanded_release_card__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    release: selectedRelease,\n                    onClose: handleCloseExpandedCard,\n                    onTrackSelect: handleTrackSelect,\n                    playingVideoId: playingVideoId\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 right-4 z-[60] w-full max-w-sm\",\n                style: {\n                    pointerEvents: isPlayerVisible ? \"auto\" : \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                    children: isPlayerVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 100\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 100\n                        },\n                        transition: {\n                            type: \"spring\",\n                            stiffness: 300,\n                            damping: 30\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-card/80 backdrop-blur-lg rounded-lg shadow-2xl p-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end mb-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-6 w-6\",\n                                        onClick: handleClosePlayer,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-video\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_youtube_embed__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        url: playerUrl,\n                                        autoplay: true\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/releases-page-client.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ReleasesPageClient, \"rVcd4Y3pbj5gkJ2/vWR6yJ6XE2c=\");\n_c = ReleasesPageClient;\nvar _c;\n$RefreshReg$(_c, \"ReleasesPageClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/releases/releases-page-client.tsx\n"));

/***/ })

});